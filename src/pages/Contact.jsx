import React from 'react'
import SparklesText from '../components/ui/sparkles-text'
import Footer from '@/components/Footer/Footer'

const Contact = () => {
  return (
    <div className="w-full bg-[#fcf2e9] overflow-x-hidden flex flex-col justify-center items-center py-4 ">
      <div className="flex flex-col justify-center items-center w-[80%] my-20 ">
        <SparklesText
          className="font-Anton text-7xl text-white font-outline-black-1 my-4 "
          text="CONTACT"
          text2="US"
        />
        <p className='font-Archivo tracking-wide font-semibold text-[#4d4844]' >
        Fill out this form, our team will get back to you as soon as possible!
        </p>
        <br />

        {/* Updated Grid to Single Column */}
        <div className="grid grid-cols-2 items-center gap-4 w-full py-4 ">
          {/* Select Input */}
   
          <div>
            <label htmlFor="occasion" className="block mb-1 font-medium">
              Choose The type 
            </label>
            <select id="occasion" className="w-full p-2 border rounded">
              <option value="wedding">Wedding</option>
              <option value="engagement">Engagement</option>
            </select>
          </div>

          {/* Text Inputs */}
          <div>
            <label htmlFor="name" className="block mb-1 font-medium">
              Enterprise
            </label>
            <input
              type="text"
              id="name"
              className="w-full p-2 border rounded"
            //   placeholder="Enterprise"
            />
          </div>

          <div>
            <label htmlFor="firstName" className="block mb-1 font-medium">
              Name
            </label>
            <input
              type="text"
              id="firstName"
              className="w-full p-2 border rounded"
            //   placeholder="Enter your name"
            />
          </div>

          <div>
            <label htmlFor="lastName" className="block mb-1 font-medium">
              Last Name
            </label>
            <input
              type="text"
              id="lastName"
              className="w-full p-2 border rounded"
            //   placeholder="Enter your last name"
            />
          </div>

          <div>
            <label htmlFor="email" className="block mb-1 font-medium">
              Email
            </label>
            <input
              type="email"
              id="email"
              className="w-full p-2 border rounded"
            //   placeholder="Enter your email"
            />
          </div>

          <div>
            <label htmlFor="phone" className="block mb-1 font-medium">
              Phone Number
            </label>
            <input
              type="tel"
              id="phone"
              className="w-full p-2 border rounded"
            //   placeholder="Enter your phone number"
            />
          </div>

          <div className="col-span-2">
            <label htmlFor="message" className="block mb-1 font-medium">
              Message
            </label>
            <textarea
              id="message"
              rows="8"
              className="w-full p-2 border rounded"
            //   placeholder="Enter your message"
            />
          </div>
        </div>
        <button className="bg-black text-white px-8 py-2 font-Anton tracking-wide ">
          Send
        </button>
      </div>
      <Footer/>
    </div>
  )
}

export default Contact
