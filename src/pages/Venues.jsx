import React from 'react'
import EventDetailsBg from '../assets/EventDetailsBg.png'
import { EventDetailslider } from '@/components/EventDetailSlider/EventDetailslider'
import WhichVenueIsRight from '@/components/WhichVenueIsRight/WhichVenueIsRight'
import WhatOurClientSay from '@/components/WhatOurClientSay/WhatOurClientSay'
import SliderEventDetails from '@/components/SliderEventDetails/SliderEventDetails'
import Followus from '@/components/Followus/Followus'
import Footer from '@/components/Footer/Footer'
import PreferredVenue from '@/components/PreferredVenue/PreferredVenue'
const Venue = () => {
  return (
    <div className="bg-[#fcf2e9] overflow-hidden  ">
      <section
        className="w-full h-screen flex justify-center items-center relative"
        style={{
          backgroundImage: `url(${EventDetailsBg})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
        }}
      >
        <h2
          style={{
            transform: 'rotate(-8deg)',
            textShadow: '8px 8px 0px rgba(0,0,0,1)',
          }}
          className="font-<PERSON> text-9xl text-white font-outline-black-1 capitalize text-center"
        >
          <span className="block">ALL VENUES</span>
        </h2>

        <div className="absolute bottom-0 left-0 w-full">
          <div className="h-2 bg-[#eb936b]"></div>
          <div className="h-2 bg-[#70b3a3]"></div>
          <div className="h-2 bg-[#f4cd77]"></div>
          <div className="h-2 bg-[#c2ad8c]"></div>
        </div>
      </section>
      <EventDetailslider />
      <PreferredVenue/>
      <WhichVenueIsRight/>
      <WhatOurClientSay/>
      <SliderEventDetails/>
      <Followus/>
      <Footer/>
    </div>
  )
}

export default Venue
