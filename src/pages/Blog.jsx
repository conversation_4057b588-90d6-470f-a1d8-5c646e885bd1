import React from "react";
import Footer from "@/components/Footer/Footer";
import WhichVenueEvent from "@/components/WhichVenueEvent/WhichVenueEvent";
import spin1 from "../assets/spin1.png";
import spin2 from "../assets/spin2.png";
import spin3 from "../assets/spin3.png";
import svg from "../assets/illu-rainbow-wave.svg.svg";
const Blog = () => {
  const images = [spin1, spin2, spin3, spin1, spin2, spin3];

  const sparkleStars = [...Array(10)].map((_, index) => {
    const size = Math.random() * 100 + 2;
    const top = Math.random() * 100;
    const left = Math.random() * 100;
    const opacity = Math.random() * 0.4 + 0.2;
    const animationDelay = Math.random() * 2;

    return (
      <span
        key={`sparkle-${index}`}
        className="absolute bg-white rounded-full animate-blink sparkle"
        style={{
          width: `${size}px`,
          height: `${size}px`,
          top: `${top}%`,
          left: `${left}%`,
          opacity: opacity,
          animationDelay: `${animationDelay}s`,
          background: '#eb9261',
        }}
      ></span>
    );
  });
  const sparkle1Stars = [...Array(10)].map((_, index) => {
    const size = Math.random() * 100 + 2;
    const top = Math.random() * 100;
    const left = Math.random() * 100;
    const opacity = Math.random() * 0.4 + 0.2;
    const animationDelay = Math.random() * 2;

    return (
      <span
        key={`sparkle1-${index}`}
        className="absolute bg-white rounded-full animate-blink sparkle1"
        style={{
          width: `${size}px`,
          height: `${size}px`,
          top: `${top}%`,
          left: `${left}%`,
          opacity: opacity,
          animationDelay: `${animationDelay}s`,
          background: "#ffc107",
        }}
      ></span>
    );
  });



  return (
    <div className="bg-[#fcf2e9] overflow-hidden  ">
      <section
        className="w-full h-screen flex justify-center items-center relative"
        style={{
          backgroundImage: `url(${svg})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
        }}
      >
          {/* Sparkle effects */}
      <div className="absolute inset-0 pointer-events-none z-0">
        {sparkleStars}
        {sparkle1Stars}
      </div>
        <h2
          style={{
            transform: "rotate(-8deg)",
            textShadow: "8px 8px 0px rgba(0,0,0,1)",
          }}
          className="font-Anton text-9xl text-white font-outline-black-1 capitalize text-center"
        >
          <span className="block">ALL</span>
          <span className="block">BLOG's</span>
        </h2>
      </section>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 my-2 px-[20px] py-[20px]">
        {images.map((image, index) => (
          <div
            key={index}
            className="rounded-2xl overflow-hidden shadow-md max-h-[70vh] flex flex-col items-center justify-center relative  "
          >
            <img
              src={image}
              alt={`Image ${index}`}
              className="w-full object-cover"
              style={{ height: "100%" }}
            />
        
            <div className="absolute bottom-0 w-full bg-black bg-opacity-20 text-white text-center py-2">
              <p className="font-Anton">Event {index}</p>
            </div>
          </div>
        ))}
      </div>

      

      <WhichVenueEvent />
      <Footer />
    </div>
  );
};

export default Blog;
