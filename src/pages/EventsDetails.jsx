import React, { useState } from "react";
import { EventDetailslider } from "@/components/EventDetailSlider/EventDetailslider";
import WhichVenueIsRight from "@/components/WhichVenueIsRight/WhichVenueIsRight";
import WhatOurClientSay from "@/components/WhatOurClientSay/WhatOurClientSay";
import SliderEventDetails from "@/components/SliderEventDetails/SliderEventDetails";
import Followus from "@/components/Followus/Followus";
import Footer from "@/components/Footer/Footer";
import { useLocation } from "react-router-dom";

const EventsDetails = () => {
  const location = useLocation();
  const { image, title, subImages } = location.state || {};

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedImage, setSelectedImage] = useState(null);


  const openModal = (image) => {
    setSelectedImage(image);
    setIsModalOpen(true);
  };


  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedImage(null);
  };
  return (
    <div className="bg-[#fcf2e9] overflow-hidden  ">
      <section
        className="w-full h-screen flex justify-center items-center relative"
        style={{
          backgroundImage: `url(${image})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
        }}
      >
        <h2
          style={{
            transform: "rotate(-8deg)",
            textShadow: "8px 8px 0px rgba(0,0,0,1)",
          }}
          className="font-Anton sm:text-9xl text-6xl text-white font-outline-black-1 capitalize text-center"
        >
          <span className="block">{title?.split(" ")[0] || "ENGAGEMENT"}</span>
          <span className="block">{title?.split(" ")[1] || "CEREMONY"}</span>
        </h2>

        {/* Border lines at the bottom */}
        <div className="absolute bottom-0 left-0 w-full">
          <div className="h-2 bg-[#eb936b]"></div>
          <div className="h-2 bg-[#70b3a3]"></div>
          <div className="h-2 bg-[#f4cd77]"></div>
          <div className="h-2 bg-[#c2ad8c]"></div>
        </div>
      </section>
      <EventDetailslider />

      <div className="sm:h-screen w-screen flex justify-center items-center sm:p-8">
        <div className="flex justify-between items-center w-[90%]">
          <div className="flex flex-col items-center p-2">
            {subImages?.slice(0, 2).map((subImage, index) => (
              <img
                key={index}
                className={`border-8 border-[#c1ab8b] sm:h-[294px] sm:w-[423px] object-cover ${
                  index === 1 ? "m-2" : ""
                }`}
                src={subImage.src}
                alt={`SubImage ${index + 1}`}
                onClick={() => openModal(subImage)} // Open modal on click
              />
            ))}
          </div>

          <div className="flex justify-center items-center p-2">
            {subImages?.slice(2, 3).map((subImage, index) => (
              <img
                key={index}
                className="m-3 border-8 border-[#c1ab8b] sm:h-[608px] sm:w-[430px] object-cover"
                src={subImage.src}
                alt={`SubImage ${index + 1}`}
                onClick={() => openModal(subImage)} // Open modal on click
              />
            ))}
          </div>

          <div className="flex flex-col items-center p-2">
            {subImages?.slice(3, 5).map((subImage, index) => (
              <img
                key={index}
                className={`border-8 border-[#c1ab8b]  sm:h-[294px] sm:w-[423px] object-cover ${
                  index === 1 ? "m-2" : ""
                }`}
                src={subImage.src}
                alt={`SubImage ${index + 4}`}
                onClick={() => openModal(subImage)} // Open modal on click
              />
            ))}
          </div>
        </div>
      </div>

      {/* Modal */}
      {isModalOpen && (
        <div
          className="fixed top-0 left-0 w-full h-[100vh]  bg-black bg-opacity-50 flex justify-center items-center z-50"
          onClick={closeModal} 
        >
          <div
            className="bg-white p-2 rounded-lg max-w-3xl w-full"
            onClick={(e) => e.stopPropagation()} 
          >
            <img
              src={selectedImage.src}
              alt={selectedImage.alt}
              className="w-full h-[80%] rounded-lg"
            />
            <button
              onClick={closeModal}
              className="absolute top-2 right-2 p-2 h-[40px] w-[40px] bg-black text-white font-bold rounded-full"
            >
              X
            </button>
          </div>
        </div>
      )}

      <WhichVenueIsRight />
      <WhatOurClientSay />
      <SliderEventDetails />
      <Followus />
      <Footer />
    </div>
  );
};

export default EventsDetails;
