import { cn } from "@/lib/utils";
import Marquee from "../components/ui/marquee";
// import Marquee from "./components/magicui/marquee";
import bghome from "../assets/bgHome.avif";
import LineGroup from "../assets/LineGroup.png";
import EdgeLineGroup from "../assets/EdgeLineGroup.png";
import Logo from "../assets/urbanvenuelogo.png";
import { VelocityScroll } from "../components/ui/scroll-based-velocity";
import { DiscoverEvents } from "@/components/DiscoverEvent/DiscoverEvent";
import { HomeSliderText } from "@/components/HomeSliderText/HomeSliderText";
import WhichVenueEvent from "@/components/WhichVenueEvent/WhichVenueEvent";
import FollowUs from "@/components/Followus/Followus";
import TeamService from "@/components/TeamService/TeamService";
import Footer from "@/components/Footer/Footer";
import homeimg from "../assets/Home.png";
import home2 from "../assets/home2.png";
import home3 from "../assets/home3.png";
import secondsection from "../assets/2section.png";
const reviews = [
  {
    name: "",
    username: "",
    body: "",
    img: homeimg,
  },
  {
    name: "OFFICE PARTY",
    username: "",
    body: "",
    img: "",
  },
  {
    name: "",
    username: "",
    body: "",
    img: home3,
  },
  {
    name: "MEHNDI",
    username: "",
    body: "",
    img: "",
  },
  {
    name: "",
    username: "",
    body: "",
    img: home2,
  },
  {
    name: "KITTY PARTY",
    username: "",
    body: "",
    img: "",
  },
  {
    name: "",
    username: "",
    body: "",
    img: homeimg,
  },
  {
    name: "MEHNDI PARTY",
    username: "",
    body: "",
    img: "",
  },
];

const firstRow = reviews.slice(0, reviews.length / 2);
const secondRow = reviews.slice(reviews.length / 2);

const ReviewCard = ({ img, name, username, body }) => {
  // Check if name is present and other parameters are empty
  const isNameOnly = name && !username && !body;

  return (
    <figure
      className={cn(
        "relative h-80 w-45 cursor-pointer overflow-hidden  rounded-xl  "
        // light styles
        //   'border-gray-950/[.1] bg-gray-950/[.01] hover:bg-gray-950/[.05]',
        // dark styles
        //   'dark:border-gray-50/[.1] dark:bg-gray-50/[.10] dark:hover:bg-gray-50/[.15]'
      )}
    >
      {isNameOnly ? (
        <div
          className="flex items-center justify-center text-6xl h-full w-full transform rotate-90 font-bold "
          style={{
            //   border: '4px solid yellow',
            //   fontSize: '2.25rem', // 4xl size
            color: "#fcf2e9",
            // background: 'none',
            // textAlign: 'start',
            // display: 'flex',
            // alignItems: 'center',
            // justifyContent: 'center',
            textShadow: `
            -1px -1px 0 #be724d,
            1px -1px 0 #be724d,
            -1px 1px 0 #be724d,
            1px 1px 0 #be724d
          `,
          }}
        >
          {name}
        </div>
      ) : (
        <>
          {img && (
            <img
              className="rounded-full object-cover w-4/6 h-80   "
              alt=""
              src={img}
            />
          )}
          <div className="flex flex-row items-center gap-2">
            <div className="flex flex-col">
              <figcaption className="text-sm font-medium dark:text-white">
                {name}
              </figcaption>
              <p className="text-xs font-medium dark:text-white/40">
                {username}
              </p>
            </div>
          </div>
          <blockquote className="mt-2 text-sm">{body}</blockquote>
        </>
      )}
    </figure>
  );
};

export function MarqueeDemoVertical() {
  return (
    <div className="relative flex h-full  w-full flex-row items-center justify-center overflow-hidden  border bg-homebg md:shadow-xl">
      <Marquee pauseOnHover vertical className="[--duration:20s]">
        {firstRow.map((review) => (
          <ReviewCard key={review.username} {...review} />
        ))}
      </Marquee>
      <Marquee reverse pauseOnHover vertical className="[--duration:20s]">
        {secondRow.map((review) => (
          <ReviewCard key={review.username} {...review} />
        ))}
      </Marquee>
      <div className="pointer-events-none absolute inset-x-0 top-0 h-1/3 bg-gradient-to-b from-white dark:from-background"></div>
      {/* <div className="pointer-events-none absolute inset-x-0 bottom-0 h-1/3 bg-gradient-to-t from-white dark:from-background"></div> */}
    </div>
  );
}
function Home() {
  return (
    <>
      <div className="flex flex-col lg:flex-row h-screen w-full bg-homebg">
        <div>
          <img src={Logo} className="absolute w-40" alt="logo" />
        </div>

        <div
          className="w-full lg:w-[50vw] h-[50vh] lg:h-[100vh] bg-cover bg-center flex flex-col justify-center items-center"
          style={{
            backgroundImage: `url(${homeimg})`,
            backgroundPosition: "center",
            // backgroundSize: 'contain',
            backgroundRepeat: "no-repeat",
          }}
        >
          <h2
            style={{
              transform: "rotateX(-28deg) rotateY(16deg)",
              textShadow: "8px 8px 0px rgba(0,0,0,1)",
            }}
            className="font-Anton text-5xl md:text-7xl lg:text-8xl text-white font-outline-black-1"
          >
            OFFICE PARTY
          </h2>
          <button
            style={{
              transform: "rotateX(-28deg) rotateY(16deg)",
            }}
            className="shadow-custom px-4 py-2 md:px-6 md:py-3 lg:px-8 lg:py-2 bg-white border border-black font-bold rounded-sm mt-4"
          >
            READ MORE
          </button>
        </div>
        <div className="w-full lg:w-[50vw] h-[50vh] lg:h-[100vh] flex justify-center items-center">
          <MarqueeDemoVertical />
        </div>
      </div>

      <div className="flex flex-col h-auto lg:h-[160vh] w-full pb-40 bg-homebg relative justify-center items-center pt-20 lg:pt-40">
        <img
          className="absolute top-[-2rem] lg:-top-20 md:w-[100%] 2xl:w-[100%]  lg:w-auto"
          src={LineGroup}
          alt=""
        />

        <div className="absolute sm:top-20 top-[2rem] md:top-30 lg:top-40 text-center z-20 px-4  flex flex-col lg:flex-row items-center justify-center">
          <h2
            style={{
              textShadow: "8px 8px 0px rgba(0,0,0,1)",
            }}
            className="font-Anton text-[3rem] md:text-7xl lg:text-9xl text-white sm:mr-7 "
          >
            MAKE MOMENT SPECIAL
          </h2>
        </div>

        <div className="w-full flex flex-col lg:flex-row items-center lg:justify-between px-4 lg:px-10 z-10 mt-20 lg:mt-40">
          <img
            className="w-full mt-20
    sm:w-[75%] 
    md:w-[60%] 
    lg:w-[50%] 
    max-h-[73%] 
    rounded-tl-[360px] 
    rounded-tr-[360px] 
    object-cover"
            src={secondsection}
            alt=""
          />
          <div className="w-full max-w-md lg:max-w-[40%] h-auto mt-10 lg:mt-0 flex flex-col justify-center">
            <h2 className="font-Anton text-lg md:text-2xl lg:text-4xl leading-[1.6] tracking-[1px] text-center lg:text-left">
              Find great places to celebrate your special occasion
            </h2>
            <p className="font-Archivo font-normal mt-4 tracking-[1px] text-sm md:text-xl text-center lg:text-left mb-[50px]">
            Looking for the perfect place to celebrate? Find amazing venues for weddings, parties, and all your special moments—just how you want them!


            </p>
          </div>
        </div>
      </div>

      <DiscoverEvents />
      <HomeSliderText />
      <WhichVenueEvent />
      <TeamService />
      <FollowUs />
      <Footer />
    </>
  );
}

export default Home;
