"use client";
import { motion, useScroll, useTransform } from "framer-motion";
import React from "react";

export default function AnimatedText() {
  const { scrollYProgress } = useScroll();

  // Animations for "MAKE MOMENT"
  const makeMomentX = useTransform(scrollYProgress, [0.2, 0.4], ["-100%", "0%"]); // Starts off-screen left
  const makeMomentOpacity = useTransform(scrollYProgress, [0.2, 0.4], [0, 1]); // Fades in
  
  // Animations for "SPECIAL"
  const specialX = useTransform(scrollYProgress, [0.4, 0.6], ["100%", "0%"]); // Starts off-screen right
  const specialOpacity = useTransform(scrollYProgress, [0.4, 0.6], [0, 1]); // Fades in

  return (
    <div className="absolute top-40 text-center z-20 w-full">
      <motion.h2
        style={{
          x: makeMomentX,
          opacity: makeMomentOpacity,
          textShadow: '8px 8px 0px rgba(0,0,0,1)',
        }}
        className="font-Anton text-9xl text-white"
      >
        MAKE MOMENT
      </motion.h2>

      <motion.h2
        style={{
          x: specialX,
          opacity: specialOpacity,
          textShadow: '8px 8px 0px rgba(0,0,0,1)',
        }}
        className="font-Anton text-9xl text-white"
      >
        SPECIAL
      </motion.h2>
    </div>
  );
}
