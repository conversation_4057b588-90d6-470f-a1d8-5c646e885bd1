import React, { useRef } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/navigation";
import "swiper/css/autoplay";
import ArrowBackIosIcon from "@mui/icons-material/ArrowBackIos";
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";
import { Autoplay, Navigation } from "swiper/modules";

const SliderTwo = () => {
  const prevRef = useRef(null);
  const nextRef = useRef(null);

  const images = [
    "https://cdn.pixabay.com/photo/2017/08/03/21/48/drinks-2578446_960_720.jpg",
    "https://cdn.pixabay.com/photo/2022/06/02/15/01/music-7238254_1280.jpg",
    "https://media.istockphoto.com/id/622215586/photo/psychedelic-concert-crowd.jpg?s=1024x1024&w=is&k=20&c=WqgRRDfxurHvXrwoYS6ypVn2HHIs0eD0knfNI_4ZeJU=",
    "https://cdn.pixabay.com/photo/2018/09/05/08/05/party-3655712_1280.jpg",
    "https://media.istockphoto.com/id/1067608094/photo/christmas-balcony-decoration.jpg?s=2048x2048&w=is&k=20&c=pWoFjeThSjNxdg_pa7cBbGqmtiqQcElL2484y5Gks_0=",
    "https://media.istockphoto.com/id/1585015684/photo/long-table-served-with-homemade-food-and-drinks-standing-in-backyard.jpg?s=1024x1024&w=is&k=20&c=bk2zjy8hi8sXyeXJ-sr5puDpy8T-Uj9sf1QMIkPMqdY=",
  ];

  return (
    <div className="bg-[#fcf2e9] overflow-hidden">
      <div className="w-full h-screen flex items-center justify-center relative">
        <div className="w-full mt-28 relative ">
          <Swiper
            spaceBetween={0}
            slidesPerView={2}
            modules={[Navigation, Autoplay]}
            autoplay={{ delay: 3000, disableOnInteraction: false }}
            navigation={{
              prevEl: prevRef.current,
              nextEl: nextRef.current,
            }}
            onBeforeInit={(swiper) => {
              swiper.params.navigation.prevEl = prevRef.current;
              swiper.params.navigation.nextEl = nextRef.current;
            }}
          >
            {images.map((image, index) => (
              <SwiperSlide
                key={index}
                className="flex justify-center "
              >
                <img
                  src={image}
                  alt={`Slide ${index + 1}`}
                  className="w-[80%] rounded-xl flex "
                />
              </SwiperSlide>
            ))}
          </Swiper>

          {/* Buttons Container */}
          <div className="absolute flex mt-2 gap-2 right-28">
            <button
              ref={prevRef}
              className="text-white rounded-full p-3 bg-white shadow-md"
            >
              <ArrowBackIosIcon className="text-black" />
            </button>
            <button
              ref={nextRef}
              className="text-white rounded-full p-3 bg-white shadow-md"
            >
              <ArrowForwardIosIcon className="text-black" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SliderTwo;
