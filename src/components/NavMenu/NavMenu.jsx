import MenuIcon from "@mui/icons-material/Menu";
import { transform } from "framer-motion";
import React, { useEffect, useState } from "react";
import Logo from "../../assets/urbanvenuelogo.png";
import team1 from "../../assets/1evedet.png";
import team2 from "../../assets/2evedet.png";
import team3 from "../../assets/3evedet.png";
import team4 from "../../assets/4evedet.png";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { ABOUT, HOME, VENUE, BLOG, EVENTS } from "@/routes/routes";
import FacebookOutlinedIcon from "@mui/icons-material/FacebookOutlined";
import InstagramIcon from "@mui/icons-material/Instagram";
import YouTubeIcon from "@mui/icons-material/YouTube";
import WhatsAppIcon from "@mui/icons-material/WhatsApp";
import PinterestIcon from "@mui/icons-material/Pinterest";
import GradualSpacing from "../ui/gradual-spacing";
import { CONTACT_US } from "@/routes/routes";


export default function NavMenu() {
  const navigate = useNavigate();
  const [open, setOpen] = useState(true);
  useEffect(() => {
    setOpen(true);
  }, []);
  const redirect = (path) => {
    setOpen((prev) => !prev);
    navigate(path);
  };
  return (
    <>
      <div
        className="rounded-full w-16 h-16 bg-black top-10 right-10 fixed z-50 cursor-pointer flex items-center justify-center"
        onClick={() => setOpen(!open)}
      >
        <MenuIcon className="text-white text-lg" />
      </div>

      {
        <div
          className={`fixed flex top-0 left-0 w-full h-screen z-40 bg-[#fcf2e9] transition ${
            open ? "translate-x-full" : "translate-x-0"
          } duration-800 ease-in origin-left`}
        >
          <div className="flex flex-col flex-1 justify-between items-start p-4  ">
            <div>
              <img src={Logo} className="w-40  " alt="" />
            </div>
            <div className="flex flex-col justify-start items-start gap-4">
              <span className="cursor-pointer" onClick={() => redirect(HOME)}>
                <GradualSpacing
                  className="font-Archivo font-semibold text-black text-5xl tracking-wide"
                  text="Home"
                />
              </span>
              <span className="cursor-pointer" onClick={() => redirect(VENUE)}>
                <GradualSpacing
                  className="font-Archivo font-semibold text-black text-5xl tracking-wide"
                  text="Venue"
                />
              </span>
              <span className="cursor-pointer" onClick={() => redirect(ABOUT)}>
                <GradualSpacing
                  className="font-Archivo font-semibold text-black text-5xl tracking-wide"
                  text="About Us"
                />
              </span>

              <span
                className="cursor-pointer"
                onClick={() => redirect(EVENTS)}
              >
                <GradualSpacing
                  className="font-Archivo font-semibold text-black text-5xl tracking-wide"
                  text="Events"
                />
              </span>
              <span className="cursor-pointer" onClick={() => redirect(BLOG)}>
                <GradualSpacing
                  className="font-Archivo font-semibold text-black text-5xl tracking-wide"
                  text="Blog"
                />
              </span>
              <span
                className="cursor-pointer"
                onClick={() => redirect(CONTACT_US)}
              >
                <GradualSpacing
                  className="font-Archivo font-semibold text-black text-5xl tracking-wide"
                  text="Career"
                />
              </span>

              <span
                className="cursor-pointer"
                onClick={() => redirect(CONTACT_US)}
              >
                <GradualSpacing
                  className="font-Archivo font-semibold text-black text-5xl tracking-wide"
                  text="Contact us"
                />
              </span>
            </div>
            <div className="flex space-x-4">
              <FacebookOutlinedIcon
                style={{ fill: "#1877F2" }}
                fontSize="large"
              />
              <InstagramIcon style={{ fill: "#ff0000" }} fontSize="large" />
              <YouTubeIcon style={{ fill: "#ff0000" }} fontSize="large" />
              <WhatsAppIcon style={{ fill: "#25d366" }} fontSize="large" />
              <PinterestIcon style={{ fill: "#bd081c" }} fontSize="large" />
            </div>
          </div>

          <div className="flex-1 flex flex-wrap justify-center items-center">
            <img
              src={team1}
              className=" p-2 w-[40%] h-[45%] rounded-2xl "
              alt=""
            />
            <img
              src={team2}
              className=" p-2 w-[40%]  h-[45%] rounded-2xl"
              alt=""
            />
            <img
              src={team3}
              className=" p-2 w-[40%]  h-[45%] rounded-2xl"
              alt=""
            />
            <img
              src={team4}
              className="  p-2 w-[40%]  h-[45%] rounded-2xl"
              alt=""
            />
            {/* <img src={team1} className=' max-w-[40%] ' alt="" /> */}
          </div>
        </div>
      }
    </>
  );
}
