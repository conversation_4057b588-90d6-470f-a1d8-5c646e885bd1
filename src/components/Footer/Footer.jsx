import React from "react";
import {
  Facebook,
  Instagram,
  YouTube,
  WhatsApp,
  Pinterest,
} from "@mui/icons-material";
import Logo from "../../assets/urbanvenuelogo.png";
import { VelocityScroll } from "../ui/singleVelocity";
const Footer = () => {
  return (
    <footer className=" text-white bg-[#2f2e2a] ">
      <div className="container mx-auto ">
        <div className="grid grid-cols-1 md:grid-cols-3 text-center md:text-left">
          {/* Menu Section */}
          <div className="flex flex-col justify-center items-center text-center border border-white p-8 px-24 border-l-transparent border-r-transparent ">
            <h6 className="text-5xl font-bold mb-2 font-Anton tracking-wide ">
              MENU
            </h6>
            <ul className="space-y-2">
              <li className="text-lg">Homepage</li>
              <li className="text-lg">Our Services</li>
              <li className="text-lg">Venues</li>
              <li className="text-lg">Contact</li>
              <li className="text-lg">Blogs</li>
            </ul>
          </div>

          {/* Logo and Social Icons */}
          <div className="flex flex-col justify-center items-center border border-white  ">
            {/* <h5 className="text-2xl font-bold mb-2">Urban Venue</h5> */}
            <img src={Logo} alt="" />
            <div className="flex space-x-4 mt-2 md:space-x-2">
              <a href="#" className="p-2 rounded-full border border-[#1877F2]">
                <Facebook />
              </a>
              <a href="#" className="p-2 rounded-full border border-[#E1306C]">
                <Instagram />
              </a>
              <a href="#" className="p-2 rounded-full border border-[#FF0000]">
                <YouTube />
              </a>
              <a href="#" className="p-2 rounded-full border border-[#25D366]">
                <WhatsApp />
              </a>
              <a href="#" className="p-2 rounded-full border border-[#E60023]">
                <Pinterest />
              </a>
            </div>
          </div>

          {/* Info Section */}
          <div className="flex flex-col justify-center items-center text-center border border-white p-8 px-24 border-l-transparent border-r-transparent">
            <h6 className="text-5xl font-bold mb-2 font-Anton tracking-wide">
            INFO
            </h6>
            <ul className="space-y-2">
              <li className="text-lg">Contact</li>
              <li className="text-lg">Confidentiality</li>
              <li className="text-lg">Privacy Policy</li>
              <li>Legal</li>
              <li>Terms & Conditions</li>
            </ul>
          </div>
        </div>

        <div className="py-2 border-0 border-b-2">
          <VelocityScroll
            text="EVENT CEREMONY"
            default_velocity={5}
            className=" my-2 font-Anton tracking-wide font-bold text-[200px]   text-transparent font-outline-orange-1"
          />
        </div>
        {/* Copyright */}
        <div className="py-8 text-center text-gray-500 text-sm">
          © Copyright 2024-2025 owntechnologies.in
        </div>
      </div>
    </footer>
  );
};

export default Footer;
