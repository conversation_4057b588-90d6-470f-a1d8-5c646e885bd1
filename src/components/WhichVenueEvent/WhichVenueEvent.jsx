import React from 'react'
import svg from '../../assets/illu-rainbow-wave.svg.svg'
import spin1 from "../../assets/spin1.png"
import spin2 from "../../assets/spin2.png"
import spin3 from "../../assets/spin3.png"
import { Link } from 'react-router-dom'
const WhichVenueEvent = () => {
  const images =[spin1,spin2,spin3]
  return (
    <div className="relative min-h-screen bg-[#fcf2e9]">
      {/* SVG as background */}
      <div
        className="absolute inset-0 bg-cover bg-center pointer-events-none z-0"
        style={{ backgroundImage: `url(${svg})` }}
      />

      {/* Content in front */}
      <div className="container mx-auto flex flex-col items-center justify-center min-h-screen px-4 py-8 relative z-10">
        <h2
          style={{
            transform: 'rotateX(-28deg) rotateY(16deg)',
            textShadow: '8px 8px 0px rgba(0,0,0,1)',
          }}
          className="font-Anton font-outline-black-1 sm:text-8xl text-4xl sm:mb-[5rem] mb-[6rem] text-white my-8 sm:w-[50%] w-[300px] "
        >
         WHAT TYPE OF VENUE ARE YOU LOOKING ?
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 my-2">
          {images.map((image,index) => (
            <div
              key={index}
              className="bg-gray-100 rounded-3xl overflow-hidden shadow-md max-h-[50vh] flex flex-col items-center justify-center relative border-8 border-[#c1ab8b]"
            >
              <img
                src={image}
                alt={`Image ${index}`}
                className="w-full object-cover"
                style={{ height: '100%' }}
              />
              {/* Text overlay */}
              <div className="absolute bottom-0 w-full bg-black bg-opacity-20 text-white text-center py-2">
                <p className="font-Anton">Event {index}</p>
              </div>
            </div>
          ))}
        </div>
        <div className="flex flex-col justify-center items-center my-4">
          <p className="text-center font-Archivo text-[16.41px] w-[86%] font-normal leading-[47.6px] tracking-[1px]">
          EXPLORE DIFFERENT VENUES TO FIND THE ONE THAT SUITS YOU BEST.
          </p>
        </div>
        {/* Button */}
        <Link to="/venue" >
        <button className=" shadow-custom font-Anton px-8 py-2 bg-white border border-black font-normal rounded-sm my-4  tracking-[1px] ">
          READ MORE
        </button>
        </Link>
      </div>
    </div>
  )
}

export default WhichVenueEvent
