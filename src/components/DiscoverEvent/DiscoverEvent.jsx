import React, { useEffect, useState } from "react";
import { VelocityScroll } from "../../components/ui/DiscoverEventvelocitytext";
import { Swiper, SwiperSlide } from "swiper/react";
import "swiper/css";
import "swiper/css/autoplay";
import { Autoplay } from "swiper/modules";
import home2 from "../../assets/home2.png";
import slider1 from "../../assets/slider1.png";
import slider2 from "../../assets/slider2.png";
import slider3 from "../../assets/slider3.png";
import { Link } from "react-router-dom";

export function DiscoverEvents() {
  const [text, setText] = useState("Wedding ceremony");
  const textw = ["Wedding ceremony", "Mehndi Ceremony", "Haldi Ceremony"];
  const images = [slider1, slider2, slider3];

  useEffect(() => {
    const interval = setInterval(() => {
      setText((prevText) => {
        const currentIndex = textw.indexOf(prevText);
        const nextIndex = (currentIndex + 1) % textw.length;
        return textw[nextIndex];
      });
    }, 5000);

    return () => clearInterval(interval);
  }, [textw]);

  return (
    <div className="w-screen h-[800px]  relative bg-[#f2cf75] overflow-hidden ">
      {/* Centered Heading */}
      <div className="absolute top-4 md:top-[5%] w-full text-center z-20 px-4">
        <h2 className="font-Anton text-4xl sm:text-6xl md:text-7xl lg:text-8xl text-black">
          DISCOVER
        </h2>
        <h2 className="font-Anton text-4xl sm:text-6xl md:text-7xl lg:text-8xl text-black">
          ALL OUR EVENTS
        </h2>
      </div>

      {/* Diagonal Velocity Scroll */}
      <div className="absolute inset-0 z-10 flex items-center justify-center overflow-hidden transform -rotate-45">
        <VelocityScroll
          text={text}
          default_velocity={2}
          className="text-[80px] sm:text-[120px] md:text-[180px] lg:text-[240px] font-bold tracking-[-0.02em] text-black drop-shadow-sm"
        />
      </div>

      {/* Foreground Auto-Moving Slider */}
      <div className="absolute inset-0 lg:mt-32  z-20 flex items-center justify-center">
        <Swiper
          modules={[Autoplay]}
          spaceBetween={50}
          slidesPerView={1}
          autoplay={{ delay: 5000, disableOnInteraction: false }}
          loop={true}
          className="max-w-[60%] max-h-[80%] w-full h-full"
        >
          {images.map((image, index) => (
            <SwiperSlide key={index}>
              <Link to="/events">
                <div className="flex flex-col mt-10 items-center justify-center text-white text-3xl font-bold rounded-2xl h-full">
                  <img
                    src={image}
                    alt={`Slide ${index + 1}`}
                    className="object-cover rounded-2xl w-full h-[400px]"
                  />
                  <h1 className="text-center font-Anton text-4xl font-normal leading-[27px] tracking-[1px] text-black stroke-slate-500 mt-4 font-outline-white-1">
                    {text}
                  </h1>
                </div>
              </Link>
            </SwiperSlide>
          ))}
        </Swiper>
      </div>
    </div>
  );
}
