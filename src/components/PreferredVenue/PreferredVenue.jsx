import React, { useState } from "react";
import svg from "../../assets/illu-rainbow-wave.svg.svg";
import spin1 from "../../assets/spin1.png";
import spin2 from "../../assets/spin2.png";
import spin3 from "../../assets/spin3.png";
import { goa, mandap1, mandap2, mandap3, mandap4, mandap5, evedet1, evedet2, evedet3, evedet4, evedet5 } from "../../assets";
import { Link } from "react-router-dom";
import { farm1507_1, farm1507_2, farm1507_3, farm1507_4, farm1507_5, farm1507_6, farm1507_7, farm1507_8 } from "../../assets";
import { farm1501_1, farm1501_2, farm1501_3, farm1501_4, farm1501_5, farm1501_6, farm1501_7, farm1501_8, farm1501_9, farm1501_10, farm1501_11, farm1501_12, farm1501_13, farm1501_14 } from "../../assets";

const data = {
  cities: [
    { name: "NEW DELHI", selectedBorder: false },
    { name: "GURUGRAM", selectedBorder: false },
    { name: "NOIDA", selectedBorder: true },
  ],
  venueTypes: [
    { name: "FARM HOUSE", selectedBackground: false },
    // { name: "PARTY TERRACE CAFE", selectedBackground: false },
    // { name: "BANQUET", selectedBackground: false },
    // { name: "APARTMENT", selectedBackground: true },
  ],
  images: {
    NOIDA: {
      // APARTMENT: [
      //   {
      //     name: "dsa",
      //     src: spin1,
      //     alt: "Image 1",
      //     subImages: [
      //       { src: goa },
      //       { src: mandap1 },
      //       { src: mandap2 },
      //       { src: mandap3 },
      //       { src: mandap4 },
      //       { src: mandap5 },
      //     ],
      //   },
      //   {
      //     src: spin2,
      //     alt: "Image 2",
      //     subImages: [
      //       { src: goa },
      //       { src: mandap1 },
      //       { src: mandap2 },
      //       { src: mandap3 },
      //       { src: mandap4 },
      //       { src: mandap5 },
      //     ],
      //   },
      //   {
      //     src: spin3,
      //     alt: "Image 3",
      //     subImages: [
      //       { src: goa },
      //       { src: mandap1 },
      //       { src: mandap2 },
      //       { src: mandap3 },
      //       { src: mandap4 },
      //       { src: mandap5 },
      //     ],
      //   },
      // ],
      // BANQUET: [
      //   {
      //     src: spin1,
      //     alt: "bImage 1",
      //     subImages: [
      //       { src: goa },
      //       { src: mandap1 },
      //       { src: mandap2 },
      //       { src: mandap3 },
      //       { src: mandap4 },
      //       { src: mandap5 },
      //     ],
      //   },
      //   {
      //     src: spin2,
      //     alt: "agdsbrImage 2",
      //     subImages: [
      //       { src: goa },
      //       { src: mandap1 },
      //       { src: mandap2 },
      //       { src: mandap3 },
      //       { src: mandap4 },
      //       { src: mandap5 },
      //     ],
      //   },
      //   {
      //     src: spin3,
      //     alt: "gdsdImage 3",
      //     subImages: [
      //       { src: goa },
      //       { src: mandap1 },
      //       { src: mandap2 },
      //       { src: mandap3 },
      //       { src: mandap4 },
      //       { src: mandap5 },
      //     ],
      //   },
      // ],
      // "PARTY TERRACE CAFE": [
      //   {
      //     src: spin1,
      //     alt: "bImACASVage 1",
      //     subImages: [
      //       { src: goa },
      //       { src: mandap1 },
      //       { src: mandap2 },
      //       { src: mandap3 },
      //       { src: mandap4 },
      //       { src: mandap5 },
      //     ],
      //   },
      //   {
      //     src: spin2,
      //     alt: "aSCSCgdsbrImage 2",
      //     subImages: [
      //       { src: goa },
      //       { src: mandap1 },
      //       { src: mandap2 },
      //       { src: mandap3 },
      //       { src: mandap4 },
      //       { src: mandap5 },
      //     ],
      //   },
      //   {
      //     src: spin3,
      //     alt: "gdsdImage 3",
      //     subImages: [
      //       { src: goa },
      //       { src: mandap1 },
      //       { src: mandap2 },
      //       { src: mandap3 },
      //       { src: mandap4 },
      //       { src: mandap5 },
      //     ],
      //   },
      //   {
      //     src: spin3,
      //     alt: "PAVILION WHOITE HALL2",
      //     subImages: [
      //       { src: goa },
      //       { src: mandap1 },
      //       { src: mandap2 },
      //       { src: mandap3 },
      //       { src: mandap4 },
      //       { src: mandap5 },
      //     ],
      //   },
      //   {
      //     src: spin1,
      //     alt: "LOTUS BANQUAT HALL2",
      //     subImages: [
      //       { src: goa },
      //       { src: mandap1 },
      //       { src: mandap2 },
      //       { src: mandap3 },
      //       { src: mandap4 },
      //       { src: mandap5 },
      //     ],
      //   },
      //   {
      //     src: spin2,
      //     alt: "LILY WHITE2",
      //     subImages: [
      //       { src: goa },
      //       { src: mandap1 },
      //       { src: mandap2 },
      //       { src: mandap3 },
      //       { src: mandap4 },
      //       { src: mandap5 },
      //     ],
      //   },
      //   {
      //     src: spin3,
      //     alt: "PAVILION WHOITE HALL2",
      //     subImages: [
      //       { src: goa },
      //       { src: mandap1 },
      //       { src: mandap2 },
      //       { src: mandap3 },
      //       { src: mandap4 },
      //       { src: mandap5 },
      //     ],
      //   },
      // ],
      "FARM HOUSE": [
        {
          src: farm1501_1,
          alt: "Farmhouse 1501",
          subImages: [
            { src: farm1501_1 },
            { src: farm1501_2 },
            { src: farm1501_3 },
            { src: farm1501_4 },
            { src: farm1501_5 },
            { src: farm1501_6 },
            { src: farm1501_7 },
            { src: farm1501_8 },
            { src: farm1501_9 },
            { src: farm1501_10 },
            { src: farm1501_11 },
            { src: farm1501_12 },
            { src: farm1501_13 },
            { src: farm1501_14 },

          ],
        },
        {
          src: farm1507_1,
          alt: "Farmhouse 1507",
          subImages: [
            { src: farm1507_1 },
            { src: farm1507_2 },
            { src: farm1507_3 },
            { src: farm1507_4 },
            { src: farm1507_5 },
            { src: farm1507_6 },
            { src: farm1507_7 },
            { src: farm1507_8 },

          ],
        },
        {
          src: spin2,
          alt: "Farmhouse 1502",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },
        {
          src: spin2,
          alt: "Farmhouse 1503",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },
        {
          src: spin3,
          alt: "Farmhouse 1505",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },
        {
          src: spin3,
          alt: "Farmhouse 1506",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },
        {
          src: spin1,
          alt: "Farmhouse 1507",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },
        {
          src: spin1,
          alt: "Farmhouse 1508",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },


      ],
    },
    "NEW DELHI": {
      APARTMENT: [
        {
          src: spin1,
          alt: "LOTUS BANQUAT HALL ",
          subImages: [
            { src: evedet1 },
            { src: evedet2 },
            { src: evedet3 },
            { src: evedet4 },
            { src: evedet5 },
            ,
          ],
        },
        {
          src: spin2,
          alt: "LILY WHITE",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },
        {
          src: spin3,
          alt: "PAVILION WHOITE HALL",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },
        {
          src: spin3,
          alt: "PAVILION WHOITE HALL2",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },
        {
          src: spin1,
          alt: "LOTUS BANQUAT HALL2",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },
        {
          src: spin2,
          alt: "LILY WHITE2",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },
        {
          src: spin3,
          alt: "PAVILION WHOITE HALL2",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },
      ],
      BANQUET: [
        {
          src: spin1,
          alt: "bImage 1",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },
        {
          src: spin2,
          alt: "agdsbrImage 2",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },
        {
          src: spin3,
          alt: "gdsdImage 3",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },
        {
          src: spin3,
          alt: "PAVILION WHOITE HALL2",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },
        {
          src: spin1,
          alt: "LOTUS BANQUAT HALL2",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },
        {
          src: spin2,
          alt: "LILY WHITE2",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },
        {
          src: spin3,
          alt: "PAVILION WHOITE HALL2",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },
      ],
      "PARTY TERRACE CAFE": [
        {
          src: spin1,
          alt: "bImACASVage 1",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },
        {
          src: spin2,
          alt: "aSCSCgdsbrImage 2",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },
        {
          src: spin3,
          alt: "gdsdImage 3",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },

        {
          src: spin1,
          alt: "LOTUS BANQUAT HALL2",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },
        {
          src: spin2,
          alt: "LILY WHITE2",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },
        {
          src: spin3,
          alt: "PAVILION WHOITE HALL2",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },
      ],
      "FARM HOUSE": [
        {
          src: spin1,
          alt: "LOTUS BANQUAT HALL",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },
        {
          src: spin2,
          alt: "LILY WHITE",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },
        {
          src: spin3,
          alt: "PAVILION WHOITE HALL2",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },
        {
          src: spin1,
          alt: "LOTUS BANQUAT HALL2",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },
        {
          src: spin2,
          alt: "LILY WHITE2",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },
        {
          src: spin3,
          alt: "PAVILION WHOITE HALL2",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },
      ],
    },
    GORGAON: {
      APARTMENT: [
        {
          name: "dsa",
          src: spin1,
          alt: "Image 1",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },
        {
          src: spin2,
          alt: "Image 2",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },
        {
          src: spin3,
          alt: "Image 3",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },
      ],
      BANQUET: [
        {
          src: spin1,
          alt: "bImage 1",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },
        {
          src: spin2,
          alt: "agdsbrImage 2",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },
        {
          src: spin3,
          alt: "gdsdImage 3",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },
      ],
      "PARTY TERRACE CAFE": [
        {
          src: spin1,
          alt: "bImACASVage 1",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },
        {
          src: spin2,
          alt: "aSCSCgdsbrImage 2",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },
        {
          src: spin3,
          alt: "gdsdImage 3",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },
      ],
      "FARM HOUSE": [
        {
          src: spin1,
          alt: "bImACASVage 1",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },
        {
          src: spin2,
          alt: "aSCSCgdsbrImage 2",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },
        {
          src: spin3,
          alt: "gdsdImage 3",
          subImages: [
            { src: goa },
            { src: mandap1 },
            { src: mandap2 },
            { src: mandap3 },
            { src: mandap4 },
            { src: mandap5 },
          ],
        },
      ],
    },
  },
};

const PreferredVenue = () => {
  const [selectedCity, setSelectedCity] = useState("NEW DELHI");
  const [selectedVenueType, setSelectedVenueType] = useState("FARM HOUSE");

  const handleCitySelect = (city) => {
    setSelectedCity(city);
  };

  const handleVenueTypeSelect = (venueType) => {
    setSelectedVenueType(venueType);
  };

  const images = data.images[selectedCity]?.[selectedVenueType] || [];

  return (
    <div className="relative min-h-screen bg-[#fcf2e9]">
      {/* SVG as background */}
      <div
        className="absolute inset-0 bg-cover bg-center pointer-events-none z-0"
        style={{ backgroundImage: `url(${svg})` }}
      />

      {/* Content in front */}
      <div className="container mx-auto flex flex-col items-center justify-center min-h-screen px-4 py-8 relative z-10">
        <h2
          style={{
            transform: "rotateX(-28deg) rotateY(16deg)",
            textShadow: "8px 8px 0px rgba(0,0,0,1)",
          }}
          className="font-Anton font-outline-black-1 sm:text-8xl text-6xl text-white my-20 text-center"
        >
          <span className="uppercase">Which venue is</span>
          <br />
          <span className="uppercase">right for you?</span>
        </h2>

        {/* City selection */}
        <div className="flex text-center mb-4">
          {data.cities.map((city) => (
            <button
              key={city.name}
              onClick={() => handleCitySelect(city.name)}
              className={`sm:py-4 py-2 sm:px-10 px-4 text-xl rounded-full font-Anton tracking-wide m-1 border ${selectedCity === city.name
                ? "border-[#baab8f]"
                : "border-transparent"
                }`}
              style={{
                background: selectedCity === city.name ? "#ffffff" : "",
              }}
            >
              {city.name}
            </button>
          ))}
        </div>

        {/* Venue type selection */}
        <div className="flex text-center mb-4 bg-[#a29f9a] rounded-full">
          {data.venueTypes.map((venueType) => (
            <button
              key={venueType.name}
              onClick={() => handleVenueTypeSelect(venueType.name)}
              className={`sm:py-4 py-2 sm:px-10 px-2 sm:text-xl text-[14px] rounded-full font-Anton tracking-wide m-1 ${selectedVenueType === venueType.name
                ? "bg-white border-4 border-[#6e6c6b]  "
                : ""
                }`}
            >
              {venueType.name}
            </button>
          ))}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 my-2">
          {images.map((image, index) => (
            <Link
              key={index}
              to="/venue/venuedetails"
              state={{ image: image.src, title: image.alt, subImages: image.subImages, }}
            >
              <div className="bg-gray-100 rounded-[30px] overflow-hidden shadow-md max-h-[60vh] flex flex-col items-center justify-center relative border-8 border-[#c1ab8b]">
                <img
                  src={image.src}
                  alt={image.alt}
                  className="w-full object-cover"
                  style={{ height: "100%" }}
                />
                {/* Text overlay */}
                <div className="absolute bottom-0 w-full bg-black bg-opacity-20 text-white text-center py-2">
                  <p className="font-Anton">{image.alt}</p>
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* Text below images */}
        <div className="flex flex-col justify-center items-center my-4">
          <p className="text-center font-Archivo text-[16.41px] w-[86%] font-normal leading-[47.6px] tracking-[1px]">
            There are many variations of passages of Lorem Ipsum available, but
            the majority.
          </p>
        </div>

        {/* Button */}
        <button className="shadow-custom font-Anton px-8 py-2 bg-white border border-black font-normal rounded-sm my-4 tracking-[1px]">
          READ MORE
        </button>
      </div>
    </div>
  );
};

export default PreferredVenue;
