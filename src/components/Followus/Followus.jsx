import React from 'react';
import svg from '../../assets/illu-rainbow-wave.svg.svg';
import InstagramIcon from '../../assets/insta.svg';
import fndspin1 from "../../assets/2ndspin1.png"
import sndspin1 from "../../assets/2ndspin2.png"
import tndspin1 from "../../assets/2ndspin3.png"
const Followus = () => {
  const images=[fndspin1 ,sndspin1,tndspin1  ]
  return (
    <div className="relative min-h-screen bg-[#fcf2e9]">
      {/* SVG as background */}
      <div
        className="absolute inset-0 bg-cover bg-center pointer-events-none z-0"
        style={{ backgroundImage: `url(${svg})` }}
      />

      {/* Content in front */}
      <div className="container mx-auto flex flex-col items-center justify-center min-h-screen px-4 py-8 relative z-10">
        <p className='font-Anton font-outline-black-1 text-xl text-[#302d2b] ' >@urbanvenue</p>
        <h2 className="font-Anton font-outline-black-1 sm:text-8xl text-4xl text-[#302d2b] my-8">
          YOU LIKE US FOLLOW US?
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 my-2">
          {images.map((image,index) => (
            <div
              key={index}
              className={`bg-gray-100 rounded-2xl overflow-hidden shadow-md max-h-[50vh] flex flex-col items-center justify-center relative ${
                index === 0 ? 'mt-12' : index === 2 ? 'mt-12' : 'mt-4'
              }`}
            >
              <img
                src={image}
                alt={`Image ${index}`}
                className="w-full object-cover"
                style={{ height: '100%', minWidth: '350px' }}
              />
            </div>
          ))}
        </div>

        {/* Instagram Button */}
        <button className="font-Anton shadow-custom p-2 bg-white border border-black font-bold rounded-full my-4 tracking-[1px]">
          <img src={InstagramIcon} alt="Instagram" />
        </button>
      </div>
    </div>
  );
};

export default Followus;
