import React, { useState } from 'react';
import wpp1 from "../../assets/wpp1.png";
import evedet3 from "../../assets/3evedet.png";
import evedet2 from "../../assets/2evedet.png";

const SliderEventDetails = () => {
  // Example slides data with background colors
  const slides = [
    { image: wpp1, head: "<PERSON> ", bgColor: "#00b0e1" },
    { image: wpp1, head: "<PERSON><PERSON><PERSON>", bgColor: "#ADFF2F" },
    { image: wpp1, head: "<PERSON><PERSON><PERSON>", bgColor: "#00b0e1" },
    { image: wpp1, head: "Akshat Garg", bgColor: "#87CEEB" },
    { image: wpp1, head: "<PERSON>oh<PERSON> Ver<PERSON>", bgColor: "#00b0e1" },
    { image: wpp1, head: "<PERSON><PERSON>", bgColor: "#FFA500" },
    { image: wpp1, head: "<PERSON><PERSON><PERSON>", bgColor: "#00b0e1" },
    { image: wpp1, head: "<PERSON><PERSON>", bgColor: "#40E0D0" },
  ];

  const [startIndex, setStartIndex] = useState(0);

  // Navigate to the previous set of slides
  const prevSlide = () => {
    setStartIndex((prev) => (prev === 0 ? slides.length - 4 : prev - 4));
  };

  // Navigate to the next set of slides
  const nextSlide = () => {
    setStartIndex((prev) => (prev + 4 >= slides.length ? 0 : prev + 4));
  };

  // Get the current visible slides
  const visibleSlides = slides.slice(startIndex, startIndex + 4);

  return (
    <div className="w-screen sm:h-[800px] flex flex-col items-center justify-center bg-gray-100 mt-16">
      {/* Slider Container */}
      <div className="flex overflow-hidden  mb-8 sm:px-20 px-2 ">
        <div className="flex sm:flex-row flex-col gap-[10px] w-full sm:space-x-4">
          {visibleSlides.map((slide, index) => (
            <div
              key={index}
              className="flex flex-col items-center justify-start shadow-lg rounded-lg overflow-hidden sm:w-1/4 w-full h-[90vh] p-4"
              style={{ backgroundColor: slide.bgColor }}
            >
              <h2 className="font-Anton mt-5 tracking-wide text-4xl font-bold text-center mb-4">{slide.head}</h2>
              <img
                src={slide.image}
                alt={slide.head}
                className="object-cover h-[500px] w-full rounded-lg"
              />
            </div>
          ))}
        </div>
      </div>

      {/* Navigation buttons */}
      <div className="flex justify-center space-x-8 mt-4">
        <button
          onClick={prevSlide}
          className="text-xl text-white bg-gray-700 px-6 py-2 rounded-full shadow-md hover:bg-gray-800 transition"
        >
          Prev
        </button>
        <button
          onClick={nextSlide}
          className="text-xl text-white bg-gray-700 px-6 py-2 rounded-full shadow-md hover:bg-gray-800 transition"
        >
          Next
        </button>
      </div>
    </div>
  );
};

export default SliderEventDetails;
